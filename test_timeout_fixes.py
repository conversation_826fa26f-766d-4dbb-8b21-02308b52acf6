#!/usr/bin/env python3
"""
Test script to verify MongoDB timeout fixes for the Nepali app.
This script tests the database connection with the new timeout settings.
"""

import asyncio
import sys
import os
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.shared.database import get_database_manager, retry_db_operation


async def test_database_connection():
    """Test basic database connection with new timeout settings."""
    print("🔍 Testing MongoDB connection with optimized timeout settings...")
    
    try:
        db_manager = get_database_manager()
        await db_manager.initialize()
        print("✅ Database connection manager initialized successfully!")
        
        # Test admin database
        async with db_manager.get_admin_db() as admin_db:
            result = await admin_db.command('ping')
            print(f"✅ Admin database ping successful: {result}")
            
            # Test a more complex operation
            collections = await admin_db.list_collection_names()
            print(f"✅ Found {len(collections)} collections in admin database")
        
        await db_manager.close()
        print("✅ Database connections closed properly!")
        return True
        
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


@retry_db_operation(max_retries=3, delay=1.0, backoff=2.0)
async def test_retry_decorator():
    """Test the retry decorator functionality."""
    print("🔍 Testing retry decorator...")
    
    # Simulate a database operation that might fail
    db_manager = get_database_manager()
    await db_manager.initialize()
    
    async with db_manager.get_admin_db() as admin_db:
        # This should succeed
        result = await admin_db.command('ping')
        print(f"✅ Retry decorator test successful: {result}")
    
    await db_manager.close()
    return True


async def test_connection_pool():
    """Test connection pooling with multiple concurrent operations."""
    print("🔍 Testing connection pooling with concurrent operations...")
    
    async def ping_database(operation_id: int):
        try:
            db_manager = get_database_manager()
            async with db_manager.get_admin_db() as admin_db:
                result = await admin_db.command('ping')
                print(f"✅ Operation {operation_id} completed: {result}")
                return True
        except Exception as e:
            print(f"❌ Operation {operation_id} failed: {e}")
            return False
    
    # Run multiple concurrent operations
    tasks = [ping_database(i) for i in range(5)]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    successful = sum(1 for r in results if r is True)
    print(f"✅ Connection pool test: {successful}/5 operations successful")
    
    return successful == 5


async def test_timeout_resilience():
    """Test that the new timeout settings handle longer operations."""
    print("🔍 Testing timeout resilience...")
    
    try:
        db_manager = get_database_manager()
        await db_manager.initialize()
        
        async with db_manager.get_admin_db() as admin_db:
            # Test a potentially slow operation
            start_time = datetime.now()
            result = await admin_db.command('serverStatus')
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            print(f"✅ Server status command completed in {duration:.2f} seconds")
            print(f"✅ Server version: {result.get('version', 'unknown')}")
        
        await db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Timeout resilience test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting MongoDB timeout fixes verification tests...")
    print("=" * 60)
    
    tests = [
        ("Basic Connection", test_database_connection),
        ("Retry Decorator", test_retry_decorator),
        ("Connection Pool", test_connection_pool),
        ("Timeout Resilience", test_timeout_resilience),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
        
        print("-" * 40)
    
    # Summary
    print("\n📊 TEST SUMMARY:")
    print("=" * 60)
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! MongoDB timeout fixes are working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
